package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 机构管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 机构管理")
@RestController
@RequestMapping("/publicbiz/agency")
@Validated
public class AgencyController {

    @Resource
    private AgencyService agencyService;

    @GetMapping("/page")
    @Operation(summary = "分页查询机构列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<PageResult<AgencyRespVO>> pageAgency(@Valid AgencyPageReqVO reqVO) {
        PageResult<AgencyRespVO> pageResult = agencyService.pageAgency(reqVO);
        return success(pageResult);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获取机构详情")
    @Parameter(name = "id", description = "机构ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:query')")
    public CommonResult<AgencyRespVO> getAgency(@PathVariable("id") Long id) {
        AgencyRespVO agency = agencyService.getAgency(id);
        return success(agency);
    }

    @PutMapping("/update")
    @Operation(summary = "更新机构审核状态")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:update')")
    public CommonResult<Boolean> updateAgency(@Valid @RequestBody AgencyUpdateReqVO reqVO) {
        agencyService.updateAgency(reqVO);
        return success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "新增机构")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:create')")
    public CommonResult<Long> createAgency(@Valid @RequestBody AgencyCreateReqVO reqVO) {
        Long agencyId = agencyService.createAgency(reqVO);
        return success(agencyId);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除机构")
    @Parameter(name = "id", description = "机构ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency:delete')")
    public CommonResult<Boolean> deleteAgency(@PathVariable("id") Long id) {
        agencyService.deleteAgency(id);
        return success(true);
    }
} 