package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.DomesticTaskGenerateRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.order.DomesticTaskService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.bztmaster.cnt.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;

/**
 * 家政服务任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DomesticTaskServiceImpl implements DomesticTaskService {

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DomesticTaskGenerateRespVO generateTasks(PublicbizOrderDO order) {
        // 参数验证
        if (order == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "订单对象不能为空");
        }

        log.info("开始生成家政服务任务，订单ID: {}", order.getId());

        // 1. 查询家政订单详情
        DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(order.getId());
        if (domesticOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政订单详情不存在");
        }

        // 2. 检查是否已生成任务
        if (hasTasksGenerated(order.getId())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "该订单已生成任务");
        }

        // 3. 根据套餐类型生成任务
        List<DomesticTaskDO> tasks = generateTasksByPackageType(order, domesticOrder);

        // 4. 批量插入任务
        List<Long> taskIds = new ArrayList<>();
        for (DomesticTaskDO task : tasks) {
            domesticTaskMapper.insert(task);
            taskIds.add(task.getId());
        }

        // 5. 更新家政订单的任务统计信息
        updateDomesticOrderTaskInfo(domesticOrder.getId(), tasks.size());

        // 6. 构建响应结果
        DomesticTaskGenerateRespVO respVO = buildGenerateResponse(order, domesticOrder, tasks, taskIds);

        log.info("家政服务任务生成完成，订单ID: {}, 生成任务数量: {}", order.getId(), tasks.size());
        return respVO;
    }

    @Override
    public List<DomesticTaskDO> getTasksByOrderId(Long orderId) {
        return domesticTaskMapper.selectList("order_id", orderId);
    }

    @Override
    public List<DomesticTaskDO> getTasksByDomesticOrderId(Long domesticOrderId) {
        return domesticTaskMapper.selectList("domestic_order_id", domesticOrderId);
    }

    @Override
    public boolean hasTasksGenerated(Long orderId) {
        Long count = domesticTaskMapper.selectCount("order_id", orderId);
        return count != null && count > 0;
    }

    @Override
    public String generateTaskNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long snowflakeId = IdUtil.getSnowflakeNextId();
        String snowflakeSuffix = String.format("%08d", snowflakeId % 100000000L);
        return "JZ" + dateStr + snowflakeSuffix;
    }

    /**
     * 根据套餐类型生成任务
     */
    private List<DomesticTaskDO> generateTasksByPackageType(PublicbizOrderDO order, DomesticOrderDO domesticOrder) {
        String packageType = domesticOrder.getServicePackageType();
        
        if ("long-term".equals(packageType)) {
            return generateLongTermTasks(order, domesticOrder);
        } else if ("count-card".equals(packageType)) {
            return generateCountCardTasks(order, domesticOrder);
        } else {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "不支持的套餐类型: " + packageType);
        }
    }

    /**
     * 生成长周期套餐任务
     */
    private List<DomesticTaskDO> generateLongTermTasks(PublicbizOrderDO order, DomesticOrderDO domesticOrder) {
        List<DomesticTaskDO> tasks = new ArrayList<>();
        
        // 解析服务周期和频次
        String serviceDuration = domesticOrder.getServicePackageDuration(); // 如：30天、26天
        String serviceFrequency = domesticOrder.getServiceFrequency(); // 如：每日
        
        // 提取天数
        int servicePeriodDays = extractDaysFromDuration(serviceDuration);
        
        // 根据频次计算任务数量
        int taskCount = calculateTaskCountByFrequency(servicePeriodDays, serviceFrequency);
        
        // 生成任务
        LocalDate startDate = domesticOrder.getServiceStartDate() != null ? 
            domesticOrder.getServiceStartDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate() :
            LocalDate.now();
            
        for (int i = 0; i < taskCount; i++) {
            DomesticTaskDO task = createBaseTask(order, domesticOrder, i + 1);
            
            // 设置排班日期（每日服务则每天一个任务）
            if ("每日".equals(serviceFrequency)) {
                task.setScheduleDate(startDate.plusDays(i));
            } else {
                // 其他频次可以根据具体需求调整
                task.setScheduleDate(startDate.plusDays(i));
            }
            
            tasks.add(task);
        }
        
        return tasks;
    }

    /**
     * 生成次数次卡套餐任务
     */
    private List<DomesticTaskDO> generateCountCardTasks(PublicbizOrderDO order, DomesticOrderDO domesticOrder) {
        List<DomesticTaskDO> tasks = new ArrayList<>();
        
        // 获取服务次数
        Integer serviceTimes = domesticOrder.getServiceTimes();
        if (serviceTimes == null || serviceTimes <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "服务次数配置错误");
        }
        
        // 解析预约时间安排
        List<LocalDate> scheduleDates = parseServiceSchedule(domesticOrder.getServiceSchedule(), serviceTimes);
        
        // 生成任务
        for (int i = 0; i < serviceTimes; i++) {
            DomesticTaskDO task = createBaseTask(order, domesticOrder, i + 1);
            
            // 设置排班日期
            if (i < scheduleDates.size()) {
                task.setScheduleDate(scheduleDates.get(i));
            } else {
                // 如果预约时间不足，使用默认规则
                LocalDate startDate = domesticOrder.getServiceStartDate() != null ? 
                    domesticOrder.getServiceStartDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate() :
                    LocalDate.now();
                task.setScheduleDate(startDate.plusDays(i));
            }
            
            tasks.add(task);
        }
        
        return tasks;
    }

    /**
     * 创建基础任务对象
     */
    private DomesticTaskDO createBaseTask(PublicbizOrderDO order, DomesticOrderDO domesticOrder, int sequence) {
        DomesticTaskDO task = new DomesticTaskDO();

        // 基础信息
        task.setOrderId(order.getId());
        task.setOrderNo(order.getOrderNo());
        task.setDomesticOrderId(domesticOrder.getId());
        task.setTaskNo(generateTaskNo());
        task.setTaskSequence(sequence);
        task.setTaskName(domesticOrder.getServicePackageName() + " - 第" + sequence + "次服务");
        task.setTaskDescription(domesticOrder.getServiceDescription());
        task.setTaskType(domesticOrder.getServiceCategoryName());
        task.setTaskStatus("pending"); // 待分配

        // 服务信息
        task.setServiceCategoryId(domesticOrder.getServiceCategoryId());
        task.setServiceCategoryName(domesticOrder.getServiceCategoryName());
        task.setDuration(domesticOrder.getServiceDuration());

        // 客户信息
        task.setCustomerId(Long.valueOf(domesticOrder.getCustomerOneid().hashCode())); // 临时处理
        task.setCustomerName(domesticOrder.getCustomerName());
        task.setCustomerPhone(domesticOrder.getCustomerPhone());
        task.setServiceAddress(domesticOrder.getServiceAddress());

        // 服务人员信息（如果已分配）
        if (StrUtil.isNotBlank(domesticOrder.getPractitionerOneid())) {
            task.setPractitionerOneid(domesticOrder.getPractitionerOneid());
            task.setPractitionerName(domesticOrder.getPractitionerName());
            task.setPractitionerPhone(domesticOrder.getPractitionerPhone());
        }

        // 审计信息
        task.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        task.setCreateTime(LocalDateTime.now());
        task.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        task.setUpdateTime(LocalDateTime.now());
        task.setDeleted(false);


        return task;
    }

    /**
     * 从服务时长字符串中提取天数
     */
    private int extractDaysFromDuration(String duration) {
        if (StrUtil.isBlank(duration)) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "服务时长配置为空");
        }

        // 提取数字部分，如 "30天" -> 30
        String numberStr = duration.replaceAll("[^0-9]", "");
        if (StrUtil.isBlank(numberStr)) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "服务时长配置格式错误: " + duration);
        }

        try {
            return Integer.parseInt(numberStr);
        } catch (NumberFormatException e) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "服务时长配置格式错误: " + duration);
        }
    }

    /**
     * 根据频次计算任务数量
     */
    private int calculateTaskCountByFrequency(int servicePeriodDays, String frequency) {
        if ("每日".equals(frequency) || "daily".equalsIgnoreCase(frequency)) {
            return servicePeriodDays;
        } else if ("每周".equals(frequency) || "weekly".equalsIgnoreCase(frequency)) {
            return (servicePeriodDays + 6) / 7; // 向上取整
        } else if ("每月".equals(frequency) || "monthly".equalsIgnoreCase(frequency)) {
            return (servicePeriodDays + 29) / 30; // 向上取整
        } else {
            // 默认按每日计算
            log.warn("未识别的服务频次: {}, 按每日计算", frequency);
            return servicePeriodDays;
        }
    }

    /**
     * 解析服务时间安排
     */
    private List<LocalDate> parseServiceSchedule(String scheduleJson, int serviceTimes) {
        List<LocalDate> dates = new ArrayList<>();

        if (StrUtil.isBlank(scheduleJson)) {
            // 如果没有预约时间，使用默认规则生成
            LocalDate startDate = LocalDate.now();
            for (int i = 0; i < serviceTimes; i++) {
                dates.add(startDate.plusDays(i * 7)); // 默认每周一次
            }
            return dates;
        }

        try {
            // 解析JSON格式的时间安排
            Map<String, Object> schedule = objectMapper.readValue(scheduleJson, new TypeReference<Map<String, Object>>() {});

            // 这里可以根据实际的JSON结构来解析
            // 示例：假设JSON格式为 {"dates": ["2025-08-14", "2025-08-21", ...]}
            if (schedule.containsKey("dates")) {
                List<String> dateStrings = (List<String>) schedule.get("dates");
                for (String dateStr : dateStrings) {
                    try {
                        dates.add(LocalDate.parse(dateStr));
                    } catch (Exception e) {
                        log.warn("解析日期失败: {}", dateStr);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析服务时间安排失败: {}", scheduleJson, e);
        }

        // 如果解析失败或数量不足，补充默认日期
        if (dates.size() < serviceTimes) {
            LocalDate lastDate = dates.isEmpty() ? LocalDate.now() : dates.get(dates.size() - 1);
            for (int i = dates.size(); i < serviceTimes; i++) {
                lastDate = lastDate.plusDays(7); // 每周一次
                dates.add(lastDate);
            }
        }

        return dates;
    }

    /**
     * 更新家政订单的任务统计信息
     */
    private void updateDomesticOrderTaskInfo(Long domesticOrderId, int taskCount) {
        // 查询现有订单
        DomesticOrderDO existingOrder = domesticOrderMapper.selectById(domesticOrderId);
        if (existingOrder != null) {
            // 更新任务统计信息
            existingOrder.setTaskCount(taskCount);
            existingOrder.setCompletedTaskCount(0);
            existingOrder.setTaskProgress(java.math.BigDecimal.ZERO);
            existingOrder.setUpdateTime(new Date());
            existingOrder.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());

            // 使用Mapper的update方法
            domesticOrderMapper.updateById(existingOrder);
        }
    }

    /**
     * 构建任务生成响应结果
     */
    private DomesticTaskGenerateRespVO buildGenerateResponse(PublicbizOrderDO order, DomesticOrderDO domesticOrder,
                                                           List<DomesticTaskDO> tasks, List<Long> taskIds) {
        DomesticTaskGenerateRespVO respVO = new DomesticTaskGenerateRespVO();

        respVO.setOrderId(order.getId());
        respVO.setOrderNo(order.getOrderNo());
        respVO.setPackageType(domesticOrder.getServicePackageType());
        respVO.setPackageName(domesticOrder.getServicePackageName());
        respVO.setTaskCount(tasks.size());
        respVO.setTaskIds(taskIds);

        // 构建详情信息
        DomesticTaskGenerateRespVO.TaskGenerateDetail detail = new DomesticTaskGenerateRespVO.TaskGenerateDetail();

        if ("long-term".equals(domesticOrder.getServicePackageType())) {
            // 长周期套餐
            String serviceDuration = domesticOrder.getServicePackageDuration();
            int servicePeriod = extractDaysFromDuration(serviceDuration);

            detail.setServicePeriod(servicePeriod);
            detail.setServiceFrequency(domesticOrder.getServiceFrequency());
            detail.setServiceTimes(tasks.size());
            detail.setGenerateRule(String.format("根据%s%s服务规则，生成%d个任务",
                serviceDuration, domesticOrder.getServiceFrequency(), tasks.size()));
        } else if ("count-card".equals(domesticOrder.getServicePackageType())) {
            // 次数次卡套餐
            detail.setServiceTimes(domesticOrder.getServiceTimes());
            detail.setGenerateRule(String.format("根据%d次服务配置，生成%d个任务",
                domesticOrder.getServiceTimes(), tasks.size()));
        }

        // 设置日期范围
        if (!tasks.isEmpty()) {
            LocalDate minDate = tasks.stream()
                .map(DomesticTaskDO::getScheduleDate)
                .min(LocalDate::compareTo)
                .orElse(LocalDate.now());
            LocalDate maxDate = tasks.stream()
                .map(DomesticTaskDO::getScheduleDate)
                .max(LocalDate::compareTo)
                .orElse(LocalDate.now());

            detail.setStartDate(minDate.toString());
            detail.setEndDate(maxDate.toString());
        }

        respVO.setDetail(detail);

        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateTasks() {
        log.info("开始批量生成家政服务任务");

        // 1. 查询符合条件的订单
        List<PublicbizOrderDO> eligibleOrders = orderMapper.selectList(
            new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getOrderType, "domestic")
                .eq(PublicbizOrderDO::getPaymentStatus, "paid")
                .eq(PublicbizOrderDO::getOrderStatus, "executing")
        );

        log.info("查询到符合条件的家政订单数量: {}", eligibleOrders.size());

        // 2. 批量处理结果统计
        int totalOrders = eligibleOrders.size();
        int successCount = 0;
        int skipCount = 0;
        int failCount = 0;
        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> failList = new ArrayList<>();

        // 3. 遍历处理每个订单
        for (PublicbizOrderDO order : eligibleOrders) {
            try {
                // 检查是否已生成任务
                if (hasTasksGenerated(order.getId())) {
                    log.debug("订单 {} 已生成任务，跳过处理", order.getOrderNo());
                    skipCount++;
                    continue;
                }

                // 生成任务
                DomesticTaskGenerateRespVO result = generateTasks(order);

                // 记录成功结果
                Map<String, Object> successItem = new HashMap<>();
                successItem.put("orderId", order.getId());
                successItem.put("orderNo", order.getOrderNo());
                successItem.put("taskCount", result.getTaskCount());
                successItem.put("packageType", result.getPackageType());
                successList.add(successItem);

                successCount++;
                log.info("订单 {} 任务生成成功，生成任务数量: {}", order.getOrderNo(), result.getTaskCount());

            } catch (Exception e) {
                // 记录失败结果
                Map<String, Object> failItem = new HashMap<>();
                failItem.put("orderId", order.getId());
                failItem.put("orderNo", order.getOrderNo());
                failItem.put("errorMessage", e.getMessage());
                failList.add(failItem);

                failCount++;
                log.error("订单 {} 任务生成失败: {}", order.getOrderNo(), e.getMessage());
            }
        }

        // 4. 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalOrders", totalOrders);
        result.put("successCount", successCount);
        result.put("skipCount", skipCount);
        result.put("failCount", failCount);
        result.put("successList", successList);
        result.put("failList", failList);

        log.info("批量生成家政服务任务完成 - 总订单数: {}, 成功: {}, 跳过: {}, 失败: {}",
                totalOrders, successCount, skipCount, failCount);

        return result;
    }
}
