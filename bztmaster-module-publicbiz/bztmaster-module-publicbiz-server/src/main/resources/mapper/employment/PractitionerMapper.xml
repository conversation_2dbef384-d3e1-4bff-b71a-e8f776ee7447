<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper">

    <sql id="selectFields">
        id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, service_type, experience_years,
        platform_status, rating, agency_id, agency_name, status, current_status, current_order_id,
        total_orders, total_income, customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <sql id="selectFieldsWithAlias">
        p.id, p.aunt_oneid, p.name, p.phone, p.id_card, p.hometown, p.age, p.gender, p.avatar, p.service_type, p.experience_years,
        p.platform_status, p.rating, p.agency_id, p.agency_name, p.status, p.current_status, p.current_order_id,
        p.total_orders, p.total_income, p.customer_satisfaction, p.create_time, p.update_time, p.creator, p.updater, p.deleted, p.tenant_id
    </sql>

    <!-- 根据阿姨OneID查询阿姨信息 -->
    <select id="selectByAuntOneId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_practitioner
        WHERE aunt_oneid = #{auntOneId}
        AND deleted = 0
    </select>

    <!-- 根据微信用户openId查询家政人员信息 -->
    <select id="selectByOpenId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO">
        SELECT <include refid="selectFieldsWithAlias"/>
        FROM publicbiz_practitioner p
        INNER JOIN mp_user m ON p.aunt_oneid = m.oneid
        WHERE m.openid = #{openId}
        AND p.deleted = 0
        AND m.deleted = 0
    </select>

    <!-- 根据手机号查询家政人员信息 -->
    <select id="selectByMobile" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO">
        SELECT <include refid="selectFieldsWithAlias"/>
        FROM publicbiz_practitioner p
        INNER JOIN mp_user m ON p.aunt_oneid = m.oneid
        WHERE m.mobile = #{mobile}
        AND p.deleted = 0
        AND m.deleted = 0
    </select>

    <!-- 查询首页阿姨列表 -->
    <select id="selectHomeAuntList" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_practitioner
        WHERE platform_status = 'cooperating'
        AND deleted = 0
        AND status = 'active'
        ORDER BY rating DESC
        LIMIT #{limit}
    </select>

</mapper>