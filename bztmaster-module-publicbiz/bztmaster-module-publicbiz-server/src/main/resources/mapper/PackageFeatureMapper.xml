<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.PackageFeatureMapper">

    <!-- 根据套餐ID列表查询特色标签 -->
    <select id="selectFeatureListByPackageIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.PackageFeatureDO">
        SELECT *
        FROM publicbiz_package_feature
        WHERE deleted = 0
            AND package_id IN
        <foreach collection="packageIds" item="packageId" open="(" separator="," close=")">
            #{packageId}
        </foreach>
        ORDER BY sort_order ASC
        LIMIT 5
    </select>

    <!-- 根据套餐ID查询特色标签 -->
    <select id="selectFeatureListByPackageId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.PackageFeatureDO">
        SELECT *
        FROM publicbiz_package_feature
        WHERE deleted = 0
            AND package_id = #{packageId}
        ORDER BY sort_order ASC
        LIMIT 5
    </select>

</mapper>
